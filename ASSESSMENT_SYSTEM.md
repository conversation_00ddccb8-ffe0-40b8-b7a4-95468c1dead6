# Enhanced Marriage Assessment System

## Overview

Sistem assessment pernikahan yang komprehensif untuk memetakan potensi permasalahan pasangan yang akan menikah melalui assessment pribadi dan menganalisa perbedaan serta persamaan. <PERSON><PERSON> perhitungan digunakan sebagai materi diskusi yang dibantu oleh konselor pernikahan.

## 8 Area Penting Pernikahan

### 1. Visi Hidup
- **Tujuan**: Memetakan aspirasi individu untuk 5-10 tahun ke depan
- **Analisis**: Menentukan apakah visi pasangan saling melengkapi atau bertentangan
- **Pertanyaan Kunci**: <PERSON><PERSON><PERSON> pri<PERSON>, per<PERSON><PERSON><PERSON><PERSON> karier, gaya hidup yang diharapkan

### 2. Keuangan
- **Tujuan**: Menilai pendekatan pengelolaan keuangan dan transparansi
- **Analisis**: Bagaimana mengelola keuangan, transparansi, dukungan keluarga besar
- **Kategori**: Financial management, transparency, family support

### 3. <PERSON><PERSON><PERSON><PERSON>
- **Tujuan**: <PERSON><PERSON><PERSON><PERSON> gaya pen<PERSON> dan filosofi mendidik anak
- **<PERSON><PERSON><PERSON>**: <PERSON><PERSON><PERSON>, <PERSON><PERSON>itatif, Permisif
- **Kategori Pendekatan**: Tidak terlibat, Attachment parenting, Free-range parenting, Tiger parenting

### 4. Komunikasi
- **Tujuan**: Menganalisis gaya komunikasi dan penyelesaian konflik
- **Kategori Gaya**: Pasif, Agresif, Pasif-Agresif, Asertif
- **Analisis**: Dominasi vs pasif dalam percakapan

### 5. Fungsi dan Peran
- **Tujuan**: Memetakan peran berdasarkan Efesus 5
- **Fokus**: Peran pria (sumber, pelindung, imam, raja, nabi) dan wanita (penolong, tunduk)
- **Analisis**: Dominasi dan keselarasan dalam peran

### 6. Keintiman Seksual
- **Tujuan**: Memahami pandangan seks dari perspektif alkitabiah
- **Analisis**: Kesepakatan frekuensi, ekspektasi, komunikasi tentang seks

### 7. Spiritualitas
- **Tujuan**: Menilai keselarasan spiritual dan pertumbuhan iman
- **Fokus**: Ibadah bersama, pelayanan, pengenalan akan Tuhan

### 8. Sisi Gelap
- **Tujuan**: Mengidentifikasi emosi negatif yang berpotensi merusak
- **Kategori**: Kemarahan, Kecemburuan, Ketidakpuasan, Sinisme, Kritik, Rengekan, Penyerangan, Pesimisme, Perfeksionisme

## Sistem Scoring

### Individual Scoring
- **Weighted Scoring**: Setiap pertanyaan memiliki bobot (weight) 1-3
- **Domain Weights**: Setiap domain memiliki bobot berbeda berdasarkan kepentingan
- **Scale Conversion**: Jawaban skala dikonversi ke nilai 0-100
- **Category Tracking**: Jawaban dikategorikan untuk analisis spesifik

### Compatibility Calculation
- **Score Difference**: Kompatibilitas = 100 - |score1 - score2|
- **Alignment Threshold**: ≥80 = Aligned, ≤50 = Conflict
- **Overall Compatibility**: Rata-rata tertimbang dari semua domain

### Domain Weights
```typescript
const DOMAIN_WEIGHTS = {
  "visi-hidup": 1.2,
  "keuangan": 1.1,
  "pengasuhan": 1.3,
  "komunikasi": 1.4,        // Highest weight
  "fungsi-dan-peran": 1.2,
  "seks": 1.0,
  "spiritualitas": 1.3,
  "sisi-gelap": 1.1,
};
```

## Result Analysis

### Compatibility Levels
- **Sangat Tinggi**: 90-100%
- **Tinggi**: 80-89%
- **Sedang**: 60-79%
- **Rendah**: 40-59%
- **Sangat Rendah**: 0-39%

### Risk Assessment
- **Low Risk**: Overall compatibility ≥80%
- **Medium Risk**: 60-79%
- **High Risk**: 40-59%
- **Critical Risk**: <40%

### Recommendations Generation
1. **Domain-Specific**: Berdasarkan area konflik
2. **Category-Based**: Berdasarkan kategori jawaban (e.g., parenting style)
3. **Priority-Based**: Fokus pada area paling kritis
4. **Counselor Notes**: Panduan khusus untuk konselor

## Usage Examples

### Basic Assessment Processing
```typescript
import { 
  processIndividualAssessment,
  processCoupleAssessment,
  generateCounselorSummary 
} from '@/lib/assessment';

// Process individual responses
const partner1Result = processIndividualAssessment(userId1, responses1);
const partner2Result = processIndividualAssessment(userId2, responses2);

// Generate couple analysis
const { compatibility, analysisReport } = processCoupleAssessment(
  partner1Result, 
  partner2Result
);

// Generate counselor summary
const counselorSummary = generateCounselorSummary(analysisReport);
```

### Progress Tracking
```typescript
import { getAssessmentProgress, getDomainCompletionStatus } from '@/lib/assessment';

// Check overall progress
const progress = getAssessmentProgress(responses);
console.log(`Progress: ${progress.progressPercentage}%`);

// Check specific domain
const domainStatus = getDomainCompletionStatus('komunikasi', responses);
console.log(`Communication domain: ${domainStatus.isComplete ? 'Complete' : 'Incomplete'}`);
```

## Key Features

### 1. Comprehensive Question Set
- 40 pertanyaan total (5 per domain)
- Berbagai tipe: multiple-choice, scale, open-ended
- Bobot dan kategori untuk setiap pertanyaan

### 2. Advanced Scoring Algorithm
- Weighted scoring berdasarkan kepentingan pertanyaan
- Domain-specific weights
- Category-based analysis

### 3. Detailed Compatibility Analysis
- Domain-by-domain comparison
- Specific insights berdasarkan kategori jawaban
- Prioritized recommendations

### 4. Counselor Support Tools
- Risk level assessment
- Session recommendations
- Action items
- Progress tracking

### 5. Biblical Foundation
- Berdasarkan Efesus 5 untuk peran suami-istri
- Perspektif alkitabiah untuk keintiman
- Nilai-nilai Kristen dalam spiritualitas

## Implementation Notes

### Database Integration
- Responses disimpan dalam format JSON
- Individual results dengan domain scores
- Couple results dengan compatibility analysis

### Validation
- Required questions validation
- Domain completion checking
- Response format validation

### Extensibility
- Mudah menambah pertanyaan baru
- Configurable weights dan categories
- Modular analysis functions

## Future Enhancements

1. **AI-Powered Insights**: Machine learning untuk pattern recognition
2. **Dynamic Questioning**: Adaptive questions berdasarkan jawaban sebelumnya
3. **Progress Tracking**: Timeline dan milestone tracking
4. **Counselor Dashboard**: Advanced analytics dan reporting
5. **Mobile Optimization**: Progressive Web App features
