// Test script for enhanced assessment system
// Run with: node test-enhanced-assessment.js

const { 
  enhancedAssessmentQuestions 
} = require('./src/lib/assessment/enhancedQuestions');

const {
  calculateDomainScore,
  calculateIndividualResult,
  calculateCompatibility,
  ASSESSMENT_DOMAINS
} = require('./src/lib/assessment/calculationLogic');

const {
  generateCoupleAnalysisReport
} = require('./src/lib/assessment/resultAnalysis');

// Test data
const testResponses1 = [
  // Visi Hidup
  { questionId: 'visi_1', answer: 'Membangun keluarga yang harmonis, mengembangkan karier di bidang teknologi, dan melayani di gereja', domain: 'visi-hidup' },
  { questionId: 'visi_2', answer: '<PERSON>a ingin menjadi senior developer dan memiliki tim sendiri', domain: 'visi-hidup' },
  { questionId: 'visi_3', answer: '<PERSON><PERSON> hidup yang seimbang antara karier dan keluarga', domain: 'visi-hidup' },
  { questionId: 'visi_4', answer: 'Sangat penting', domain: 'visi-hidup' },
  { questionId: 'visi_5', answer: 'Sangat mendukung', domain: 'visi-hidup' },
  
  // Keuangan
  { questionId: 'keuangan_1', answer: 'Keduanya setara', domain: 'keuangan' },
  { questionId: 'keuangan_2', answer: 'Sepenuhnya transparan', domain: 'keuangan' },
  { questionId: 'keuangan_3', answer: 'Diskusi bersama untuk menentukan alokasi yang adil', domain: 'keuangan' },
  { questionId: 'keuangan_4', answer: 'Dukung saat darurat', domain: 'keuangan' },
  { questionId: 'keuangan_5', answer: 'Sangat penting', domain: 'keuangan' },
  
  // Pengasuhan
  { questionId: 'pengasuhan_1', answer: 'Otoritatif', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_2', answer: 'Attachment parenting', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_3', answer: 'Sangat penting', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_4', answer: 'Diskusi dan konsekuensi natural', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_5', answer: 'Banyak', domain: 'pengasuhan' },
  
  // Komunikasi
  { questionId: 'komunikasi_1', answer: 'Asertif', domain: 'komunikasi' },
  { questionId: 'komunikasi_2', answer: 'Diskusi terbuka dan mencari solusi bersama', domain: 'komunikasi' },
  { questionId: 'komunikasi_3', answer: 'Sangat nyaman', domain: 'komunikasi' },
  { questionId: 'komunikasi_4', answer: 'Seimbang', domain: 'komunikasi' },
  { questionId: 'komunikasi_5', answer: 'Mendengarkan dan mencoba memahami sudut pandangnya', domain: 'komunikasi' },
  
  // Fungsi dan Peran
  { questionId: 'peran_1', answer: 'Sebagai partner yang setara dalam mengambil keputusan', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_2', answer: 'Agak setuju', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_3', answer: 'Agak setuju', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_4', answer: 'Sangat nyaman', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_5', answer: 'Pembagian berdasarkan kemampuan dan kesepakatan', domain: 'fungsi-dan-peran' },
  
  // Seks
  { questionId: 'seks_1', answer: 'Seks adalah anugerah Tuhan untuk pasangan suami istri', domain: 'seks' },
  { questionId: 'seks_2', answer: 'Sangat penting', domain: 'seks' },
  { questionId: 'seks_3', answer: 'Ya', domain: 'seks' },
  { questionId: 'seks_4', answer: 'Keintiman yang saling menghormati dan memuaskan', domain: 'seks' },
  { questionId: 'seks_5', answer: 'Agak setuju', domain: 'seks' },
  
  // Spiritualitas
  { questionId: 'spiritualitas_1', answer: 'Setiap minggu', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_2', answer: 'Sangat penting', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_3', answer: 'Ya', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_4', answer: 'Doa pribadi dan membaca Alkitab secara rutin', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_5', answer: 'Sangat selaras', domain: 'spiritualitas' },
  
  // Sisi Gelap
  { questionId: 'sisigelap_1', answer: 'Perfeksionisme', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_2', answer: 'Mengambil waktu untuk menenangkan diri dan berdoa', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_3', answer: 'Jarang', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_4', answer: 'Jarang', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_5', answer: 'Berdoa dan meminta bantuan konselor jika perlu', domain: 'sisi-gelap' },
];

const testResponses2 = [
  // Visi Hidup - sedikit berbeda
  { questionId: 'visi_1', answer: 'Membangun keluarga yang bahagia, mengembangkan bisnis sendiri, dan aktif di komunitas', domain: 'visi-hidup' },
  { questionId: 'visi_2', answer: 'Saya ingin memiliki bisnis sendiri di bidang kuliner', domain: 'visi-hidup' },
  { questionId: 'visi_3', answer: 'Gaya hidup yang fokus pada keluarga dan bisnis', domain: 'visi-hidup' },
  { questionId: 'visi_4', answer: 'Sangat penting', domain: 'visi-hidup' },
  { questionId: 'visi_5', answer: 'Agak mendukung', domain: 'visi-hidup' },
  
  // Keuangan - sama
  { questionId: 'keuangan_1', answer: 'Keduanya setara', domain: 'keuangan' },
  { questionId: 'keuangan_2', answer: 'Sebagian besar transparan', domain: 'keuangan' },
  { questionId: 'keuangan_3', answer: 'Diskusi bersama untuk menentukan alokasi yang adil', domain: 'keuangan' },
  { questionId: 'keuangan_4', answer: 'Dukung saat darurat', domain: 'keuangan' },
  { questionId: 'keuangan_5', answer: 'Sangat penting', domain: 'keuangan' },
  
  // Pengasuhan - berbeda
  { questionId: 'pengasuhan_1', answer: 'Permisif', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_2', answer: 'Free-range parenting', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_3', answer: 'Agak penting', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_4', answer: 'Memberikan kebebasan untuk belajar dari kesalahan', domain: 'pengasuhan' },
  { questionId: 'pengasuhan_5', answer: 'Kebebasan penuh dengan keamanan', domain: 'pengasuhan' },
  
  // Komunikasi - sedikit berbeda
  { questionId: 'komunikasi_1', answer: 'Pasif', domain: 'komunikasi' },
  { questionId: 'komunikasi_2', answer: 'Menghindari konflik dan mencari jalan tengah', domain: 'komunikasi' },
  { questionId: 'komunikasi_3', answer: 'Agak nyaman', domain: 'komunikasi' },
  { questionId: 'komunikasi_4', answer: 'Pasif', domain: 'komunikasi' },
  { questionId: 'komunikasi_5', answer: 'Biasanya mengalah untuk menjaga keharmonisan', domain: 'komunikasi' },
  
  // Fungsi dan Peran - sama
  { questionId: 'peran_1', answer: 'Sebagai pendukung dan penolong dalam keluarga', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_2', answer: 'Sangat setuju', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_3', answer: 'Sangat setuju', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_4', answer: 'Sangat nyaman', domain: 'fungsi-dan-peran' },
  { questionId: 'peran_5', answer: 'Pembagian berdasarkan peran tradisional', domain: 'fungsi-dan-peran' },
  
  // Seks - sama
  { questionId: 'seks_1', answer: 'Seks adalah anugerah Tuhan untuk pasangan suami istri', domain: 'seks' },
  { questionId: 'seks_2', answer: 'Sangat penting', domain: 'seks' },
  { questionId: 'seks_3', answer: 'Agak', domain: 'seks' },
  { questionId: 'seks_4', answer: 'Keintiman yang saling menghormati', domain: 'seks' },
  { questionId: 'seks_5', answer: 'Agak setuju', domain: 'seks' },
  
  // Spiritualitas - sama
  { questionId: 'spiritualitas_1', answer: 'Setiap minggu', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_2', answer: 'Sangat penting', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_3', answer: 'Ya', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_4', answer: 'Doa pribadi dan membaca Alkitab secara rutin', domain: 'spiritualitas' },
  { questionId: 'spiritualitas_5', answer: 'Sangat selaras', domain: 'spiritualitas' },
  
  // Sisi Gelap - berbeda
  { questionId: 'sisigelap_1', answer: 'Kecemburuan', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_2', answer: 'Berbicara dengan teman dekat atau keluarga', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_3', answer: 'Kadang-kadang', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_4', answer: 'Kadang-kadang', domain: 'sisi-gelap' },
  { questionId: 'sisigelap_5', answer: 'Berusaha mengontrol diri dan berbicara dengan pasangan', domain: 'sisi-gelap' },
];

function runTest() {
  console.log('🧪 Testing Enhanced Assessment System\n');
  
  // Test 1: Domain Questions
  console.log('1. Testing Domain Questions:');
  console.log(`   Total domains: ${ASSESSMENT_DOMAINS.length}`);
  ASSESSMENT_DOMAINS.forEach(domain => {
    const questions = enhancedAssessmentQuestions[domain];
    console.log(`   ${domain}: ${questions.length} questions`);
  });
  console.log('');
  
  // Test 2: Individual Assessment
  console.log('2. Testing Individual Assessment:');
  const individual1 = calculateIndividualResult('user1', testResponses1);
  const individual2 = calculateIndividualResult('user2', testResponses2);
  
  console.log(`   Partner 1 Overall Score: ${individual1.overallScore}%`);
  console.log(`   Partner 1 Categories:`, individual1.categories);
  console.log(`   Partner 2 Overall Score: ${individual2.overallScore}%`);
  console.log(`   Partner 2 Categories:`, individual2.categories);
  console.log('');
  
  // Test 3: Compatibility Analysis
  console.log('3. Testing Compatibility Analysis:');
  const compatibility = calculateCompatibility(individual1, individual2);
  console.log(`   Overall Compatibility: ${compatibility.overallCompatibility}%`);
  console.log(`   Alignment Areas: ${compatibility.alignmentAreas.join(', ')}`);
  console.log(`   Conflict Areas: ${compatibility.conflictAreas.join(', ')}`);
  console.log(`   Recommendations: ${compatibility.recommendations.length} items`);
  console.log('');
  
  // Test 4: Analysis Report
  console.log('4. Testing Analysis Report:');
  const analysisReport = generateCoupleAnalysisReport(compatibility);
  console.log(`   Compatibility Level: ${analysisReport.compatibilityLevel}`);
  console.log(`   Strength Areas: ${analysisReport.strengthAreas.length} items`);
  console.log(`   Challenge Areas: ${analysisReport.challengeAreas.length} items`);
  console.log(`   Priority Recommendations: ${analysisReport.priorityRecommendations.length} items`);
  console.log(`   Counselor Notes: ${analysisReport.counselorNotes.length} items`);
  console.log('');
  
  // Test 5: Domain Scores Detail
  console.log('5. Domain Compatibility Scores:');
  Object.entries(compatibility.compatibilityScores).forEach(([domain, score]) => {
    const status = score >= 80 ? '✅' : score >= 60 ? '⚠️' : '❌';
    console.log(`   ${domain}: ${score}% ${status}`);
  });
  console.log('');
  
  console.log('✅ All tests completed successfully!');
  console.log('\n📊 Sample Analysis Report:');
  console.log('='.repeat(50));
  console.log(`Overall Compatibility: ${analysisReport.overallCompatibility}% (${analysisReport.compatibilityLevel})`);
  console.log('\nKey Insights:');
  analysisReport.priorityRecommendations.slice(0, 3).forEach((rec, idx) => {
    console.log(`${idx + 1}. ${rec}`);
  });
}

// Run the test
try {
  runTest();
} catch (error) {
  console.error('❌ Test failed:', error.message);
  console.error(error.stack);
}
