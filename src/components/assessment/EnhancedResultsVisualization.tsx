"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  AlertCircle,
  CheckCircle,
  Heart,
  MessageCircle,
  Users,
  TrendingUp,
  TrendingDown,
  Minus,
} from "lucide-react";
import { CoupleAnalysisReport, DomainAnalysis } from "@/lib/assessment";

interface EnhancedResultsVisualizationProps {
  analysisReport: CoupleAnalysisReport;
  showCounselorNotes?: boolean;
}

const EnhancedResultsVisualization: React.FC<EnhancedResultsVisualizationProps> = ({
  analysisReport,
  showCounselorNotes = false,
}) => {
  const {
    overallCompatibility,
    compatibilityLevel,
    domainAnalyses,
    strength<PERSON><PERSON><PERSON>,
    challenge<PERSON><PERSON><PERSON>,
    priority<PERSON><PERSON><PERSON>mendations,
    counselor<PERSON><PERSON>,
  } = analysisReport;

  // Get compatibility color
  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return "text-green-600";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  // Get compatibility icon
  const getCompatibilityIcon = (status: 'aligned' | 'moderate' | 'conflict') => {
    switch (status) {
      case 'aligned':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'moderate':
        return <Minus className="h-5 w-5 text-yellow-600" />;
      case 'conflict':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
    }
  };

  // Get domain icon
  const getDomainIcon = (domain: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      "visi-hidup": "🔭",
      "keuangan": "💰",
      "pengasuhan": "👶",
      "komunikasi": <MessageCircle className="h-5 w-5" />,
      "fungsi-dan-peran": <Users className="h-5 w-5" />,
      "seks": <Heart className="h-5 w-5" />,
      "spiritualitas": "✝️",
      "sisi-gelap": "🌑",
    };
    return iconMap[domain] || "📋";
  };

  return (
    <div className="space-y-6">
      {/* Overall Compatibility */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Kompatibilitas Keseluruhan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <div className={`text-4xl font-bold ${getCompatibilityColor(overallCompatibility)}`}>
              {overallCompatibility}%
            </div>
            <Badge 
              variant={overallCompatibility >= 70 ? "default" : "destructive"}
              className="text-lg px-4 py-2"
            >
              {compatibilityLevel}
            </Badge>
            <Progress value={overallCompatibility} className="h-3" />
          </div>
        </CardContent>
      </Card>

      {/* Strength and Challenge Areas */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Area Kekuatan</CardTitle>
          </CardHeader>
          <CardContent>
            {strengthAreas.length > 0 ? (
              <div className="space-y-2">
                {strengthAreas.map((area, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span>{area}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">Tidak ada area kekuatan yang teridentifikasi</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-red-700">Area Tantangan</CardTitle>
          </CardHeader>
          <CardContent>
            {challengeAreas.length > 0 ? (
              <div className="space-y-2">
                {challengeAreas.map((area, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span>{area}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">Tidak ada area tantangan yang teridentifikasi</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Domain Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Analisis per Domain</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {domainAnalyses.map((domain, index) => (
              <div key={index} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getDomainIcon(domain.domain)}
                    <div>
                      <h4 className="font-semibold">{domain.title}</h4>
                      <p className="text-sm text-muted-foreground">{domain.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getCompatibilityIcon(domain.status)}
                    <span className={`font-semibold ${getCompatibilityColor(domain.compatibilityScore)}`}>
                      {domain.compatibilityScore}%
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Partner 1: </span>
                    <span className="font-medium">{domain.partner1Score}%</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Partner 2: </span>
                    <span className="font-medium">{domain.partner2Score}%</span>
                  </div>
                </div>

                <Progress value={domain.compatibilityScore} className="h-2" />

                {domain.insights.length > 0 && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <h5 className="font-medium text-blue-900 mb-2">Insights:</h5>
                    <ul className="text-sm text-blue-800 space-y-1">
                      {domain.insights.map((insight, idx) => (
                        <li key={idx}>• {insight}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {domain.recommendations.length > 0 && (
                  <div className="bg-yellow-50 p-3 rounded-lg">
                    <h5 className="font-medium text-yellow-900 mb-2">Rekomendasi:</h5>
                    <ul className="text-sm text-yellow-800 space-y-1">
                      {domain.recommendations.map((rec, idx) => (
                        <li key={idx}>• {rec}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {index < domainAnalyses.length - 1 && <Separator />}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Priority Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-orange-700">Rekomendasi Prioritas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {priorityRecommendations.map((recommendation, index) => (
              <Alert key={index} className="border-orange-200 bg-orange-50">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  {recommendation}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Counselor Notes (if enabled) */}
      {showCounselorNotes && counselorNotes.length > 0 && (
        <Card className="border-purple-200">
          <CardHeader>
            <CardTitle className="text-purple-700">Catatan untuk Konselor</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {counselorNotes.map((note, index) => (
                <div key={index} className="bg-purple-50 p-3 rounded-lg">
                  <p className="text-purple-800 text-sm">{note}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedResultsVisualization;
