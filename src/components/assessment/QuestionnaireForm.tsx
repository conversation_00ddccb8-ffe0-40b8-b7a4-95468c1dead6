"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "../ui/card";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Label } from "../ui/label";
import { Checkbox } from "../ui/checkbox";
import { Progress } from "../ui/progress";
import { ChevronLeft, ChevronRight, Save } from "lucide-react";
import { enhancedAssessmentQuestions } from "@/lib/assessment/enhancedQuestions";
import { getQuestionsForDomain } from "@/lib/assessment/assessmentUtils";
import { fetchQuestionsByDomain } from "@/lib/assessment/supabaseQuestions";

interface Question {
  id: string;
  type: "multiple-choice" | "scenario" | "ranking" | "open-ended" | "scale";
  text: string;
  options?: string[];
  required?: boolean;
  domain?: string;
  weight?: number;
  category?: string;
}

interface QuestionnaireFormProps {
  domain?: string;
  questions?: Question[];
  onSubmit?: (responses: Record<string, any>) => void;
  onSave?: (responses: Record<string, any>) => void;
}

const defaultDomain = "keuangan";

const QuestionnaireForm: React.FC<QuestionnaireFormProps> = ({
  domain = defaultDomain,
  questions,
  onSubmit,
  onSave,
}) => {
  const [domainQuestions, setDomainQuestions] = useState<Question[]>([]);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Use provided questions if available
    if (questions) {
      setDomainQuestions(questions);
      return;
    }

    // Otherwise fetch from Supabase
    const loadQuestions = async () => {
      try {
        setLoading(true);
        setError(null);

        const fetchedQuestions = await fetchQuestionsByDomain(domain);

        if (fetchedQuestions && fetchedQuestions.length > 0) {
          setDomainQuestions(fetchedQuestions);
        } else {
          // Fallback to enhanced local questions if no questions found in Supabase
          const localQuestions = getQuestionsForDomain(domain);
          if (localQuestions.length > 0) {
            setDomainQuestions(localQuestions);
          } else {
            // Fallback to keuangan domain if the specified domain doesn't exist
            setDomainQuestions(getQuestionsForDomain("keuangan"));
          }
        }
      } catch (err) {
        console.error("Error loading questions:", err);
        setError("Failed to load questions. Using local fallback.");

        // Use enhanced local questions as fallback
        const localQuestions = getQuestionsForDomain(domain);
        if (localQuestions.length > 0) {
          setDomainQuestions(localQuestions);
        } else {
          setDomainQuestions(getQuestionsForDomain("keuangan"));
        }
      } finally {
        setLoading(false);
      }
    };

    loadQuestions();
  }, [domain, questions]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const currentQuestion = domainQuestions[currentQuestionIndex] || {
    id: "",
    type: "multiple-choice" as const,
    text: "Loading...",
  };
  const progress =
    domainQuestions.length > 0
      ? ((currentQuestionIndex + 1) / domainQuestions.length) * 100
      : 0;

  const handleNext = () => {
    // Validate current question if required
    if (
      domainQuestions.length > 0 &&
      currentQuestion.required &&
      !responses[currentQuestion.id]
    ) {
      setErrors({
        ...errors,
        [currentQuestion.id]: "This question requires an answer",
      });
      return;
    }

    // Clear error if exists
    if (errors[currentQuestion.id]) {
      const newErrors = { ...errors };
      delete newErrors[currentQuestion.id];
      setErrors(newErrors);
    }

    // Move to next question or submit if last question
    if (currentQuestionIndex < domainQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleSubmit = () => {
    // Check if all required questions are answered
    const unansweredRequired = domainQuestions
      .filter((q) => q.required)
      .filter((q) => !responses[q.id]);

    if (unansweredRequired.length > 0) {
      const newErrors: Record<string, string> = {};
      unansweredRequired.forEach((q) => {
        newErrors[q.id] = "This question requires an answer";
      });
      setErrors(newErrors);

      // Go to the first unanswered required question
      const firstUnansweredIndex = domainQuestions.findIndex(
        (q) => q.id === unansweredRequired[0].id,
      );
      setCurrentQuestionIndex(firstUnansweredIndex);
      return;
    }

    // Submit responses
    if (onSubmit) {
      onSubmit(responses);
    }
  };

  const handleSave = () => {
    if (onSave) {
      onSave(responses);
    }
  };

  const handleResponseChange = (questionId: string, value: any) => {
    setResponses({
      ...responses,
      [questionId]: value,
    });

    // Clear error if exists
    if (errors[questionId]) {
      const newErrors = { ...errors };
      delete newErrors[questionId];
      setErrors(newErrors);
    }
  };

  const renderQuestionInput = () => {
    switch (currentQuestion.type) {
      case "multiple-choice":
        return (
          <RadioGroup
            value={responses[currentQuestion.id] || ""}
            onValueChange={(value) =>
              handleResponseChange(currentQuestion.id, value)
            }
            className="space-y-3 mt-4"
          >
            {currentQuestion.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`option-${index}`} />
                <Label htmlFor={`option-${index}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        );

      case "scale":
        return (
          <RadioGroup
            value={responses[currentQuestion.id] || ""}
            onValueChange={(value) =>
              handleResponseChange(currentQuestion.id, value)
            }
            className="space-y-3 mt-4"
          >
            {currentQuestion.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`scale-${index}`} />
                <Label htmlFor={`scale-${index}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        );

      case "scenario":
        return (
          <RadioGroup
            value={responses[currentQuestion.id] || ""}
            onValueChange={(value) =>
              handleResponseChange(currentQuestion.id, value)
            }
            className="space-y-3 mt-4"
          >
            {currentQuestion.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`scenario-${index}`} />
                <Label htmlFor={`scenario-${index}`}>{option}</Label>
              </div>
            ))}
          </RadioGroup>
        );

      case "ranking":
        // Simple implementation of ranking using checkboxes
        // In a real app, you'd want a drag-and-drop or more interactive ranking UI
        return (
          <div className="space-y-4 mt-4">
            {currentQuestion.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    min="1"
                    max={currentQuestion.options?.length}
                    className="w-16"
                    value={responses[currentQuestion.id]?.[option] || ""}
                    onChange={(e) => {
                      const value = e.target.value;
                      const currentRankings =
                        responses[currentQuestion.id] || {};
                      handleResponseChange(currentQuestion.id, {
                        ...currentRankings,
                        [option]: value,
                      });
                    }}
                  />
                  <Label>{option}</Label>
                </div>
              </div>
            ))}
          </div>
        );

      case "open-ended":
        return (
          <Textarea
            className="mt-4"
            placeholder="Type your answer here..."
            value={responses[currentQuestion.id] || ""}
            onChange={(e) =>
              handleResponseChange(currentQuestion.id, e.target.value)
            }
            rows={5}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto bg-white">
      <CardHeader>
        <CardTitle className="text-2xl">{domain} Assessment</CardTitle>
        <div className="mt-2">
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between mt-1 text-sm text-gray-500">
            <span>
              Question {currentQuestionIndex + 1} of {domainQuestions.length}
            </span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
        </div>
        {loading && (
          <p className="text-sm text-blue-500 mt-2">Loading questions...</p>
        )}
        {error && <p className="text-sm text-red-500 mt-2">{error}</p>}
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium">{currentQuestion.text}</h3>
            {errors[currentQuestion.id] && (
              <p className="text-sm text-red-500 mt-1">
                {errors[currentQuestion.id]}
              </p>
            )}
            {renderQuestionInput()}
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between">
        <div>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
        </div>

        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Progress
          </Button>

          <Button onClick={handleNext}>
            {currentQuestionIndex < domainQuestions.length - 1 ? (
              <>
                Next
                <ChevronRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              "Submit"
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default QuestionnaireForm;
