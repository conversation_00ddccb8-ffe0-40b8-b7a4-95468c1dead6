// Main exports for the enhanced assessment system

// Questions and data
export { enhancedAssessmentQuestions } from './enhancedQuestions';
export type { Question } from './questions';

// Calculation logic
export {
  calculateDomainScore,
  calculateIndividualResult,
  calculateCompatibility,
  ASSESSMENT_DOMAINS
} from './calculationLogic';

export type {
  AssessmentResponse,
  DomainScore,
  IndividualResult,
  CompatibilityResult
} from './calculationLogic';

// Result analysis
export {
  generateCoupleAnalysisReport
} from './resultAnalysis';

export type {
  DomainAnalysis,
  CoupleAnalysisReport
} from './resultAnalysis';

// Utility functions
export {
  getQuestionsForDomain,
  getAllDomains,
  formatDomainName,
  validateResponses,
  processIndividualAssessment,
  processCoupleAssessment,
  getAssessmentProgress,
  generateCounselorSummary,
  formatResponsesForDatabase,
  parseResponsesFromDatabase,
  getDomainCompletionStatus
} from './assessmentUtils';

// Re-export the original questions for backward compatibility
export { assessmentQuestions } from './questions';
