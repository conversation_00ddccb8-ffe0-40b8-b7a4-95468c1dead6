import { Question } from './questions';
import { enhancedAssessmentQuestions } from './enhancedQuestions';

// Types for assessment responses and results
export interface AssessmentResponse {
  questionId: string;
  answer: string | number | string[];
  domain: string;
}

export interface DomainScore {
  domain: string;
  score: number;
  category?: string;
  subcategories?: Record<string, number>;
}

export interface IndividualResult {
  userId: string;
  domainScores: DomainScore[];
  overallScore: number;
  categories: Record<string, string>; // e.g., "parenting-style": "Otoritatif"
  responses: AssessmentResponse[];
}

export interface CompatibilityResult {
  coupleId: string;
  partner1: IndividualResult;
  partner2: IndividualResult;
  compatibilityScores: Record<string, number>; // domain compatibility scores
  overallCompatibility: number;
  alignmentAreas: string[];
  conflictAreas: string[];
  recommendations: string[];
}

// Scoring weights for different domains
const DOMAIN_WEIGHTS = {
  "visi-hidup": 1.2,
  "keuangan": 1.1,
  "pengasuhan": 1.3,
  "komunikasi": 1.4,
  "fungsi-dan-peran": 1.2,
  "seks": 1.0,
  "spiritualitas": 1.3,
  "sisi-gelap": 1.1,
};

// Calculate individual domain score
export function calculateDomainScore(
  domain: string,
  responses: AssessmentResponse[]
): DomainScore {
  const questions = enhancedAssessmentQuestions[domain];
  if (!questions) {
    throw new Error(`Domain ${domain} not found`);
  }

  const domainResponses = responses.filter(r => r.domain === domain);
  let totalScore = 0;
  let totalWeight = 0;
  const subcategories: Record<string, number> = {};

  domainResponses.forEach(response => {
    const question = questions.find(q => q.id === response.questionId);
    if (!question) return;

    const weight = question.weight || 1;
    let score = 0;

    // Calculate score based on question type
    if (question.type === 'scale' || question.type === 'multiple-choice') {
      if (question.options && typeof response.answer === 'string') {
        const optionIndex = question.options.indexOf(response.answer);
        if (optionIndex !== -1) {
          // Convert to 0-100 scale
          score = (optionIndex / (question.options.length - 1)) * 100;
        }
      }
    } else if (question.type === 'open-ended') {
      // For open-ended questions, assign neutral score
      score = 75; // Neutral score, will be manually reviewed
    }

    totalScore += score * weight;
    totalWeight += weight;

    // Track category scores
    if (question.category) {
      if (!subcategories[question.category]) {
        subcategories[question.category] = 0;
      }
      subcategories[question.category] += score;
    }
  });

  const finalScore = totalWeight > 0 ? totalScore / totalWeight : 0;

  return {
    domain,
    score: Math.round(finalScore),
    subcategories,
  };
}

// Calculate overall individual score
export function calculateIndividualResult(
  userId: string,
  responses: AssessmentResponse[]
): IndividualResult {
  const domains = Object.keys(enhancedAssessmentQuestions);
  const domainScores: DomainScore[] = [];
  const categories: Record<string, string> = {};

  // Calculate scores for each domain
  domains.forEach(domain => {
    const domainScore = calculateDomainScore(domain, responses);
    domainScores.push(domainScore);
  });

  // Extract categories from responses
  responses.forEach(response => {
    const allQuestions = Object.values(enhancedAssessmentQuestions).flat();
    const question = allQuestions.find(q => q.id === response.questionId);
    
    if (question?.category && typeof response.answer === 'string') {
      categories[question.category] = response.answer;
    }
  });

  // Calculate weighted overall score
  let totalWeightedScore = 0;
  let totalWeight = 0;

  domainScores.forEach(domainScore => {
    const weight = DOMAIN_WEIGHTS[domainScore.domain as keyof typeof DOMAIN_WEIGHTS] || 1;
    totalWeightedScore += domainScore.score * weight;
    totalWeight += weight;
  });

  const overallScore = Math.round(totalWeightedScore / totalWeight);

  return {
    userId,
    domainScores,
    overallScore,
    categories,
    responses,
  };
}

// Calculate compatibility between two partners
export function calculateCompatibility(
  partner1: IndividualResult,
  partner2: IndividualResult
): CompatibilityResult {
  const compatibilityScores: Record<string, number> = {};
  const alignmentAreas: string[] = [];
  const conflictAreas: string[] = [];
  const recommendations: string[] = [];

  // Calculate domain-by-domain compatibility
  partner1.domainScores.forEach(domain1 => {
    const domain2 = partner2.domainScores.find(d => d.domain === domain1.domain);
    if (!domain2) return;

    // Calculate compatibility score (inverse of difference)
    const scoreDifference = Math.abs(domain1.score - domain2.score);
    const compatibilityScore = Math.max(0, 100 - scoreDifference);
    compatibilityScores[domain1.domain] = compatibilityScore;

    // Determine alignment or conflict
    if (compatibilityScore >= 80) {
      alignmentAreas.push(domain1.domain);
    } else if (compatibilityScore <= 50) {
      conflictAreas.push(domain1.domain);
    }
  });

  // Generate specific recommendations based on categories
  generateRecommendations(partner1, partner2, recommendations);

  // Calculate overall compatibility
  const domainCompatibilityScores = Object.values(compatibilityScores);
  const overallCompatibility = domainCompatibilityScores.length > 0
    ? Math.round(domainCompatibilityScores.reduce((sum, score) => sum + score, 0) / domainCompatibilityScores.length)
    : 0;

  return {
    coupleId: `${partner1.userId}_${partner2.userId}`,
    partner1,
    partner2,
    compatibilityScores,
    overallCompatibility,
    alignmentAreas,
    conflictAreas,
    recommendations,
  };
}

// Generate specific recommendations based on assessment results
function generateRecommendations(
  partner1: IndividualResult,
  partner2: IndividualResult,
  recommendations: string[]
): void {
  // Check parenting style compatibility
  const p1ParentingStyle = partner1.categories['parenting-style'];
  const p2ParentingStyle = partner2.categories['parenting-style'];
  
  if (p1ParentingStyle && p2ParentingStyle && p1ParentingStyle !== p2ParentingStyle) {
    recommendations.push(
      `Diskusikan perbedaan gaya pengasuhan: ${p1ParentingStyle} vs ${p2ParentingStyle}. Pertimbangkan untuk mencari pendekatan yang seimbang.`
    );
  }

  // Check communication style compatibility
  const p1CommStyle = partner1.categories['communication-style'];
  const p2CommStyle = partner2.categories['communication-style'];
  
  if (p1CommStyle && p2CommStyle) {
    if ((p1CommStyle === 'Pasif' && p2CommStyle === 'Agresif') ||
        (p1CommStyle === 'Agresif' && p2CommStyle === 'Pasif')) {
      recommendations.push(
        'Perbedaan gaya komunikasi yang signifikan terdeteksi. Pertimbangkan pelatihan komunikasi untuk mencapai keseimbangan.'
      );
    }
  }

  // Check biblical role alignment
  const p1MaleRole = partner1.categories['biblical-male-role'];
  const p2MaleRole = partner2.categories['biblical-male-role'];
  const p1FemaleRole = partner1.categories['biblical-female-role'];
  const p2FemaleRole = partner2.categories['biblical-female-role'];

  if (p1MaleRole && p2MaleRole && 
      Math.abs(getScaleValue(p1MaleRole) - getScaleValue(p2MaleRole)) > 2) {
    recommendations.push(
      'Diskusikan pandangan tentang peran pria dalam pernikahan berdasarkan Efesus 5 untuk mencapai pemahaman bersama.'
    );
  }

  // Check dark side emotions
  const p1DarkEmotion = partner1.categories['negative-emotion'];
  const p2DarkEmotion = partner2.categories['negative-emotion'];
  
  if (p1DarkEmotion && p1DarkEmotion !== 'Tidak ada') {
    recommendations.push(
      `Partner 1 perlu perhatian khusus untuk mengatasi kecenderungan ${p1DarkEmotion.toLowerCase()}.`
    );
  }
  
  if (p2DarkEmotion && p2DarkEmotion !== 'Tidak ada') {
    recommendations.push(
      `Partner 2 perlu perhatian khusus untuk mengatasi kecenderungan ${p2DarkEmotion.toLowerCase()}.`
    );
  }
}

// Helper function to convert scale answers to numeric values
function getScaleValue(answer: string): number {
  const scaleMap: Record<string, number> = {
    'Sangat tidak setuju': 1,
    'Agak tidak setuju': 2,
    'Netral': 3,
    'Agak setuju': 4,
    'Sangat setuju': 5,
  };
  return scaleMap[answer] || 3;
}

// Export domain list for easy access
export const ASSESSMENT_DOMAINS = Object.keys(enhancedAssessmentQuestions);
