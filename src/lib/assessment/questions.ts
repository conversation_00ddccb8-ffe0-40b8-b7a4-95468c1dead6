export interface Question {
    id: string;
    type: "multiple-choice" | "scenario" | "ranking" | "open-ended" | "scale";
    text: string;
    options?: string[];
    required?: boolean;
    domain: string;
    weight?: number; // For scoring calculations
    category?: string; // For categorization (e.g., parenting style, communication type)
}

export const assessmentQuestions: Record<string, Question[]> = {
    vision: [
        {
            id: "vision_q1",
            domain: "vision",
            type: "multiple-choice",
            text: "How important is having a shared life vision with your partner?",
            options: [
                "Not important",
                "Somewhat important",
                "Neutral",
                "Important",
                "Very important",
            ],
            required: true,
        },
        {
            id: "vision_q2",
            domain: "vision",
            type: "scenario",
            text: "If your partner received a job offer that required moving to another city, what would you expect to happen?",
            options: [
                "They should decline it to maintain our current lifestyle",
                "We would discuss it together and make a joint decision",
                "I would support their career and be willing to move",
                "We would try a long-distance relationship temporarily",
            ],
            required: true,
        },
        {
            id: "vision_q3",
            domain: "vision",
            type: "ranking",
            text: "Rank the following life goals from most important (1) to least important (4):",
            options: [
                "Career advancement",
                "Family life",
                "Personal growth",
                "Community involvement",
            ],
            required: true,
        },
        {
            id: "vision_q4",
            domain: "vision",
            type: "open-ended",
            text: "Describe where you see yourself and your relationship in 10 years:",
            required: false,
        },
    ],
    finances: [
        {
            id: "finances_q1",
            domain: "finances",
            type: "multiple-choice",
            text: "How important is financial transparency in your relationship?",
            options: [
                "Not important",
                "Somewhat important",
                "Neutral",
                "Important",
                "Very important",
            ],
            required: true,
        },
        {
            id: "finances_q2",
            domain: "finances",
            type: "scenario",
            text: "If your partner received an unexpected bonus of $5,000, what would you expect them to do?",
            options: [
                "Save all of it for our future",
                "Discuss together how to allocate it",
                "Split it between savings and personal spending",
                "They should decide since they earned it",
            ],
            required: true,
        },
        {
            id: "finances_q3",
            domain: "finances",
            type: "ranking",
            text: "Rank the following financial priorities from most important (1) to least important (4):",
            options: [
                "Building emergency savings",
                "Paying off debt",
                "Investing for retirement",
                "Saving for major purchases (home, car)",
            ],
            required: true,
        },
        {
            id: "finances_q4",
            domain: "finances",
            type: "open-ended",
            text: "Describe your ideal approach to managing household finances with your partner:",
            required: false,
        },
    ],
    parenting: [
        {
            id: "parenting_q1",
            domain: "parenting",
            type: "multiple-choice",
            text: "How important is having a unified parenting approach with your partner?",
            options: [
                "Not important",
                "Somewhat important",
                "Neutral",
                "Important",
                "Very important",
            ],
            required: true,
        },
        {
            id: "parenting_q2",
            domain: "parenting",
            type: "scenario",
            text: "If your child repeatedly breaks an important house rule, how would you handle it?",
            options: [
                "Strict consequences to teach discipline",
                "Discussion about why rules matter",
                "Natural consequences approach",
                "Flexible approach depending on circumstances",
            ],
            required: true,
        },
        {
            id: "parenting_q3",
            domain: "parenting",
            type: "ranking",
            text: "Rank the following parenting values from most important (1) to least important (4):",
            options: [
                "Teaching responsibility",
                "Nurturing creativity",
                "Building strong character",
                "Academic achievement",
            ],
            required: true,
        },
        {
            id: "parenting_q4",
            domain: "parenting",
            type: "open-ended",
            text: "Describe your philosophy on discipline and setting boundaries for children:",
            required: false,
        },
    ],
    communication: [
        {
            id: "communication_q1",
            domain: "communication",
            type: "multiple-choice",
            text: "How comfortable are you discussing difficult topics with your partner?",
            options: [
                "Very uncomfortable",
                "Somewhat uncomfortable",
                "Neutral",
                "Somewhat comfortable",
                "Very comfortable",
            ],
            required: true,
        },
        {
            id: "communication_q2",
            domain: "communication",
            type: "scenario",
            text: "When you and your partner disagree on an important issue, what typically happens?",
            options: [
                "We avoid the topic to keep peace",
                "We discuss until we reach a compromise",
                "One of us usually gives in",
                "We seek outside help to resolve it",
            ],
            required: true,
        },
        {
            id: "communication_q3",
            domain: "communication",
            type: "ranking",
            text: "Rank the following communication skills from most important (1) to least important (4):",
            options: [
                "Active listening",
                "Expressing feelings clearly",
                "Conflict resolution",
                "Non-verbal communication",
            ],
            required: true,
        },
        {
            id: "communication_q4",
            domain: "communication",
            type: "open-ended",
            text: "Describe how you prefer to receive feedback or criticism from your partner:",
            required: false,
        },
    ],
    roles: [
        {
            id: "roles_q1",
            domain: "roles",
            type: "multiple-choice",
            text: "How important is having clearly defined roles in your relationship?",
            options: [
                "Not important",
                "Somewhat important",
                "Neutral",
                "Important",
                "Very important",
            ],
            required: true,
        },
        {
            id: "roles_q2",
            domain: "roles",
            type: "scenario",
            text: "How should household responsibilities be divided?",
            options: [
                "Based on traditional gender roles",
                "Equally split all tasks",
                "Based on individual preferences and strengths",
                "Flexible and changing as needed",
            ],
            required: true,
        },
        {
            id: "roles_q3",
            domain: "roles",
            type: "ranking",
            text: "Rank the following aspects of relationship roles from most important (1) to least important (4):",
            options: [
                "Equal partnership",
                "Clear responsibilities",
                "Flexibility to change",
                "Playing to individual strengths",
            ],
            required: true,
        },
        {
            id: "roles_q4",
            domain: "roles",
            type: "open-ended",
            text: "Describe your ideal division of responsibilities in your household:",
            required: false,
        },
    ],
    sexuality: [
        {
            id: "sexuality_q1",
            domain: "sexuality",
            type: "multiple-choice",
            text: "How important is physical intimacy in maintaining a healthy relationship?",
            options: [
                "Not important",
                "Somewhat important",
                "Neutral",
                "Important",
                "Very important",
            ],
            required: true,
        },
        {
            id: "sexuality_q2",
            domain: "sexuality",
            type: "scenario",
            text: "If you and your partner have different levels of desire for intimacy, how would you address it?",
            options: [
                "Compromise to meet in the middle",
                "Seek professional counseling",
                "Accept the difference and adjust expectations",
                "Open communication to find creative solutions",
            ],
            required: true,
        },
        {
            id: "sexuality_q3",
            domain: "sexuality",
            type: "ranking",
            text: "Rank the following aspects of intimacy from most important (1) to least important (4):",
            options: [
                "Emotional connection",
                "Physical satisfaction",
                "Open communication",
                "Frequency of intimacy",
            ],
            required: true,
        },
        {
            id: "sexuality_q4",
            domain: "sexuality",
            type: "open-ended",
            text: "Describe how you believe intimacy contributes to a healthy marriage:",
            required: false,
        },
    ],
    spirituality: [
        {
            id: "spirituality_q1",
            domain: "spirituality",
            type: "multiple-choice",
            text: "How important is sharing spiritual beliefs with your partner?",
            options: [
                "Not important",
                "Somewhat important",
                "Neutral",
                "Important",
                "Very important",
            ],
            required: true,
        },
        {
            id: "spirituality_q2",
            domain: "spirituality",
            type: "scenario",
            text: "If you and your partner have different spiritual beliefs, how would you approach raising children?",
            options: [
                "Expose them to both beliefs and let them choose",
                "Agree on one primary spiritual tradition",
                "Focus on shared values rather than specific traditions",
                "Separate spiritual education based on each parent",
            ],
            required: true,
        },
        {
            id: "spirituality_q3",
            domain: "spirituality",
            type: "ranking",
            text: "Rank the following spiritual practices from most important (1) to least important (4):",
            options: [
                "Regular worship attendance",
                "Prayer/meditation together",
                "Discussing spiritual topics",
                "Service to others/community",
            ],
            required: true,
        },
        {
            id: "spirituality_q4",
            domain: "spirituality",
            type: "open-ended",
            text: "Describe how you envision spirituality playing a role in your marriage:",
            required: false,
        },
    ],
    darkside: [
        {
            id: "darkside_q1",
            domain: "darkside",
            type: "multiple-choice",
            text: "How comfortable are you discussing past traumas or mistakes with your partner?",
            options: [
                "Very uncomfortable",
                "Somewhat uncomfortable",
                "Neutral",
                "Somewhat comfortable",
                "Very comfortable",
            ],
            required: true,
        },
        {
            id: "darkside_q2",
            domain: "darkside",
            type: "scenario",
            text: "If you notice unhealthy patterns from your family of origin appearing in your relationship, how would you address them?",
            options: [
                "Ignore them and focus on the present",
                "Discuss them openly with my partner",
                "Seek professional counseling",
                "Work on personal growth independently",
            ],
            required: true,
        },
        {
            id: "darkside_q3",
            domain: "darkside",
            type: "ranking",
            text: "Rank the following potential relationship challenges from most concerning (1) to least concerning (4):",
            options: [
                "Communication breakdown",
                "Financial conflicts",
                "Different values/priorities",
                "External family interference",
            ],
            required: true,
        },
        {
            id: "darkside_q4",
            domain: "darkside",
            type: "open-ended",
            text: "Describe any patterns or behaviors you're concerned might negatively impact your relationship:",
            required: false,
        },
    ],
};
