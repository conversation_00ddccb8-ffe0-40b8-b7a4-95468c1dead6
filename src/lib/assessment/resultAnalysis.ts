import { CompatibilityResult, IndividualResult } from './calculationLogic';

// Detailed analysis interfaces
export interface DomainAnalysis {
  domain: string;
  title: string;
  description: string;
  partner1Score: number;
  partner2Score: number;
  compatibilityScore: number;
  status: 'aligned' | 'moderate' | 'conflict';
  insights: string[];
  recommendations: string[];
}

export interface CoupleAnalysisReport {
  overallCompatibility: number;
  compatibilityLevel: 'Sangat Tinggi' | 'Tinggi' | 'Sedang' | 'Rendah' | 'Sangat Rendah';
  domainAnalyses: DomainAnalysis[];
  strengthAreas: string[];
  challengeAreas: string[];
  priorityRecommendations: string[];
  counselorNotes: string[];
}

// Domain titles and descriptions in Indonesian
const DOMAIN_INFO = {
  "visi-hidup": {
    title: "Visi Hidup",
    description: "Keselarasan tujuan dan aspirasi jangka panjang dalam pernikahan"
  },
  "keuangan": {
    title: "<PERSON><PERSON><PERSON>",
    description: "Pendekatan terhadap pengelolaan keuangan dan transparansi finansial"
  },
  "pengasuhan": {
    title: "<PERSON><PERSON><PERSON><PERSON>",
    description: "Gaya dan filosofi dalam mendidik dan membesarkan anak"
  },
  "komunikasi": {
    title: "Komunikasi",
    description: "Cara berkomunikasi dan menyelesaikan konflik dalam hubungan"
  },
  "fungsi-dan-peran": {
    title: "Fungsi dan Peran",
    description: "Pemahaman tentang peran suami-istri berdasarkan nilai-nilai Alkitab"
  },
  "seks": {
    title: "Keintiman Seksual",
    description: "Pandangan dan ekspektasi tentang keintiman dalam pernikahan"
  },
  "spiritualitas": {
    title: "Spiritualitas",
    description: "Keselarasan dalam pertumbuhan iman dan praktik spiritual bersama"
  },
  "sisi-gelap": {
    title: "Sisi Gelap",
    description: "Pengelolaan emosi negatif dan potensi masalah dalam hubungan"
  }
};

// Generate comprehensive analysis report
export function generateCoupleAnalysisReport(
  compatibilityResult: CompatibilityResult
): CoupleAnalysisReport {
  const { partner1, partner2, compatibilityScores, overallCompatibility } = compatibilityResult;
  
  // Determine compatibility level
  const compatibilityLevel = getCompatibilityLevel(overallCompatibility);
  
  // Generate domain analyses
  const domainAnalyses: DomainAnalysis[] = [];
  
  Object.entries(compatibilityScores).forEach(([domain, score]) => {
    const analysis = generateDomainAnalysis(domain, partner1, partner2, score);
    domainAnalyses.push(analysis);
  });
  
  // Identify strength and challenge areas
  const strengthAreas = domainAnalyses
    .filter(d => d.status === 'aligned')
    .map(d => d.title);
    
  const challengeAreas = domainAnalyses
    .filter(d => d.status === 'conflict')
    .map(d => d.title);
  
  // Generate priority recommendations
  const priorityRecommendations = generatePriorityRecommendations(domainAnalyses, partner1, partner2);
  
  // Generate counselor notes
  const counselorNotes = generateCounselorNotes(domainAnalyses, partner1, partner2);
  
  return {
    overallCompatibility,
    compatibilityLevel,
    domainAnalyses,
    strengthAreas,
    challengeAreas,
    priorityRecommendations,
    counselorNotes,
  };
}

// Generate analysis for a specific domain
function generateDomainAnalysis(
  domain: string,
  partner1: IndividualResult,
  partner2: IndividualResult,
  compatibilityScore: number
): DomainAnalysis {
  const domainInfo = DOMAIN_INFO[domain as keyof typeof DOMAIN_INFO];
  const p1Score = partner1.domainScores.find(d => d.domain === domain)?.score || 0;
  const p2Score = partner2.domainScores.find(d => d.domain === domain)?.score || 0;
  
  const status = getCompatibilityStatus(compatibilityScore);
  const insights = generateDomainInsights(domain, partner1, partner2, compatibilityScore);
  const recommendations = generateDomainRecommendations(domain, partner1, partner2, status);
  
  return {
    domain,
    title: domainInfo?.title || domain,
    description: domainInfo?.description || '',
    partner1Score: p1Score,
    partner2Score: p2Score,
    compatibilityScore,
    status,
    insights,
    recommendations,
  };
}

// Determine compatibility status
function getCompatibilityStatus(score: number): 'aligned' | 'moderate' | 'conflict' {
  if (score >= 80) return 'aligned';
  if (score >= 60) return 'moderate';
  return 'conflict';
}

// Get compatibility level description
function getCompatibilityLevel(score: number): 'Sangat Tinggi' | 'Tinggi' | 'Sedang' | 'Rendah' | 'Sangat Rendah' {
  if (score >= 90) return 'Sangat Tinggi';
  if (score >= 80) return 'Tinggi';
  if (score >= 60) return 'Sedang';
  if (score >= 40) return 'Rendah';
  return 'Sangat Rendah';
}

// Generate insights for specific domains
function generateDomainInsights(
  domain: string,
  partner1: IndividualResult,
  partner2: IndividualResult,
  compatibilityScore: number
): string[] {
  const insights: string[] = [];
  
  switch (domain) {
    case 'pengasuhan':
      const p1ParentingStyle = partner1.categories['parenting-style'];
      const p2ParentingStyle = partner2.categories['parenting-style'];
      
      if (p1ParentingStyle && p2ParentingStyle) {
        if (p1ParentingStyle === p2ParentingStyle) {
          insights.push(`Kedua pasangan memiliki gaya pengasuhan yang sama: ${p1ParentingStyle}`);
        } else {
          insights.push(`Perbedaan gaya pengasuhan: Partner 1 (${p1ParentingStyle}) vs Partner 2 (${p2ParentingStyle})`);
        }
      }
      break;
      
    case 'komunikasi':
      const p1CommStyle = partner1.categories['communication-style'];
      const p2CommStyle = partner2.categories['communication-style'];
      
      if (p1CommStyle && p2CommStyle) {
        insights.push(`Gaya komunikasi: Partner 1 (${p1CommStyle}) vs Partner 2 (${p2CommStyle})`);
        
        if (p1CommStyle === 'Asertif' && p2CommStyle === 'Asertif') {
          insights.push('Kedua pasangan memiliki gaya komunikasi yang ideal untuk hubungan yang sehat');
        }
      }
      break;
      
    case 'fungsi-dan-peran':
      const p1MaleRole = partner1.categories['biblical-male-role'];
      const p2MaleRole = partner2.categories['biblical-male-role'];
      
      if (p1MaleRole && p2MaleRole) {
        insights.push(`Pandangan tentang peran pria: Partner 1 (${p1MaleRole}) vs Partner 2 (${p2MaleRole})`);
      }
      break;
      
    case 'sisi-gelap':
      const p1DarkEmotion = partner1.categories['negative-emotion'];
      const p2DarkEmotion = partner2.categories['negative-emotion'];
      
      if (p1DarkEmotion && p1DarkEmotion !== 'Tidak ada') {
        insights.push(`Partner 1 cenderung mengalami ${p1DarkEmotion.toLowerCase()}`);
      }
      
      if (p2DarkEmotion && p2DarkEmotion !== 'Tidak ada') {
        insights.push(`Partner 2 cenderung mengalami ${p2DarkEmotion.toLowerCase()}`);
      }
      break;
  }
  
  // Add general compatibility insight
  if (compatibilityScore >= 80) {
    insights.push('Area ini menunjukkan keselarasan yang baik antara kedua pasangan');
  } else if (compatibilityScore <= 50) {
    insights.push('Area ini memerlukan perhatian khusus dan diskusi mendalam');
  }
  
  return insights;
}

// Generate domain-specific recommendations
function generateDomainRecommendations(
  domain: string,
  partner1: IndividualResult,
  partner2: IndividualResult,
  status: 'aligned' | 'moderate' | 'conflict'
): string[] {
  const recommendations: string[] = [];
  
  if (status === 'conflict') {
    switch (domain) {
      case 'komunikasi':
        recommendations.push('Ikuti pelatihan komunikasi untuk pasangan');
        recommendations.push('Praktikkan teknik mendengarkan aktif');
        recommendations.push('Tetapkan aturan untuk diskusi yang konstruktif');
        break;
        
      case 'pengasuhan':
        recommendations.push('Diskusikan filosofi pengasuhan sebelum memiliki anak');
        recommendations.push('Baca buku tentang pengasuhan bersama-sama');
        recommendations.push('Konsultasi dengan ahli pengasuhan anak');
        break;
        
      case 'keuangan':
        recommendations.push('Buat rencana keuangan bersama');
        recommendations.push('Diskusikan transparansi keuangan');
        recommendations.push('Konsultasi dengan perencana keuangan');
        break;
        
      case 'spiritualitas':
        recommendations.push('Diskusikan harapan spiritual dalam pernikahan');
        recommendations.push('Cari mentor spiritual untuk pasangan');
        recommendations.push('Rencanakan aktivitas spiritual bersama');
        break;
    }
  } else if (status === 'aligned') {
    recommendations.push('Pertahankan keselarasan yang sudah baik di area ini');
    recommendations.push('Gunakan kekuatan ini untuk mendukung area lain yang memerlukan perbaikan');
  }
  
  return recommendations;
}

// Generate priority recommendations for the couple
function generatePriorityRecommendations(
  domainAnalyses: DomainAnalysis[],
  partner1: IndividualResult,
  partner2: IndividualResult
): string[] {
  const recommendations: string[] = [];
  
  // Focus on conflict areas first
  const conflictAreas = domainAnalyses.filter(d => d.status === 'conflict');
  
  if (conflictAreas.length > 0) {
    recommendations.push('Prioritaskan diskusi mendalam tentang area-area konflik yang teridentifikasi');
    
    // Specific high-priority recommendations
    if (conflictAreas.some(d => d.domain === 'komunikasi')) {
      recommendations.push('PRIORITAS TINGGI: Perbaiki pola komunikasi sebelum menikah');
    }
    
    if (conflictAreas.some(d => d.domain === 'fungsi-dan-peran')) {
      recommendations.push('PRIORITAS TINGGI: Klarifikasi ekspektasi peran dalam pernikahan');
    }
  }
  
  // Add general recommendations
  recommendations.push('Lakukan sesi konseling pra-nikah dengan konselor yang berpengalaman');
  recommendations.push('Buat rencana konkret untuk mengatasi area-area yang memerlukan perbaikan');
  
  return recommendations;
}

// Generate notes for counselors
function generateCounselorNotes(
  domainAnalyses: DomainAnalysis[],
  partner1: IndividualResult,
  partner2: IndividualResult
): string[] {
  const notes: string[] = [];
  
  // Overall assessment
  const conflictCount = domainAnalyses.filter(d => d.status === 'conflict').length;
  const alignedCount = domainAnalyses.filter(d => d.status === 'aligned').length;
  
  notes.push(`Jumlah area konflik: ${conflictCount}/8`);
  notes.push(`Jumlah area selaras: ${alignedCount}/8`);
  
  // Specific counselor guidance
  if (conflictCount >= 4) {
    notes.push('PERHATIAN: Banyak area konflik terdeteksi. Pertimbangkan sesi konseling intensif.');
  }
  
  // Check for critical combinations
  const criticalDomains = ['komunikasi', 'fungsi-dan-peran', 'spiritualitas'];
  const criticalConflicts = domainAnalyses.filter(d => 
    criticalDomains.includes(d.domain) && d.status === 'conflict'
  );
  
  if (criticalConflicts.length >= 2) {
    notes.push('PERINGATAN: Konflik di area-area fundamental pernikahan terdeteksi.');
  }
  
  return notes;
}
