-- Create admin_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create counselor_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS counselor_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  bio TEXT,
  specialization TEXT,
  years_experience INTEGER,
  profile_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create counselor_couple_assignments table if it doesn't exist
-- Note: Using UUID for couple_id to match the couples table
CREATE TABLE IF NOT EXISTS counselor_couple_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  counselor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL REFERENCES couples(couple_id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'completed'))
);

-- Create counseling_sessions table if it doesn't exist
-- Note: Using UUID for couple_id to match the couples table
CREATE TABLE IF NOT EXISTS counseling_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  counselor_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  couple_id UUID NOT NULL REFERENCES couples(couple_id) ON DELETE CASCADE,
  session_date TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER DEFAULT 60,
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable row level security
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE counselor_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE counselor_couple_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE counseling_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for admin_users
DROP POLICY IF EXISTS "Admins can view all admin_users" ON admin_users;
CREATE POLICY "Admins can view all admin_users"
  ON admin_users FOR SELECT
  USING (auth.uid() IN (SELECT user_id FROM admin_users));

DROP POLICY IF EXISTS "Admins can insert admin_users" ON admin_users;
CREATE POLICY "Admins can insert admin_users"
  ON admin_users FOR INSERT
  WITH CHECK (auth.uid() IN (SELECT user_id FROM admin_users));

-- Create policies for counselor_profiles
DROP POLICY IF EXISTS "Counselors can view their own profile" ON counselor_profiles;
CREATE POLICY "Counselors can view their own profile"
  ON counselor_profiles FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all counselor profiles" ON counselor_profiles;
CREATE POLICY "Admins can view all counselor profiles"
  ON counselor_profiles FOR SELECT
  USING (auth.uid() IN (SELECT user_id FROM admin_users));

DROP POLICY IF EXISTS "Counselors can update their own profile" ON counselor_profiles;
CREATE POLICY "Counselors can update their own profile"
  ON counselor_profiles FOR UPDATE
  USING (auth.uid() = user_id);

-- Create policies for counselor_couple_assignments
DROP POLICY IF EXISTS "Counselors can view their own assignments" ON counselor_couple_assignments;
CREATE POLICY "Counselors can view their own assignments"
  ON counselor_couple_assignments FOR SELECT
  USING (auth.uid() = counselor_id);

DROP POLICY IF EXISTS "Admins can manage all assignments" ON counselor_couple_assignments;
CREATE POLICY "Admins can manage all assignments"
  ON counselor_couple_assignments FOR ALL
  USING (auth.uid() IN (SELECT user_id FROM admin_users));

-- Create policies for counseling_sessions
DROP POLICY IF EXISTS "Counselors can view their own sessions" ON counseling_sessions;
CREATE POLICY "Counselors can view their own sessions"
  ON counseling_sessions FOR SELECT
  USING (auth.uid() = counselor_id);

DROP POLICY IF EXISTS "Counselors can update their own sessions" ON counseling_sessions;
CREATE POLICY "Counselors can update their own sessions"
  ON counseling_sessions FOR UPDATE
  USING (auth.uid() = counselor_id);

DROP POLICY IF EXISTS "Admins can manage all sessions" ON counseling_sessions;
CREATE POLICY "Admins can manage all sessions"
  ON counseling_sessions FOR ALL
  USING (auth.uid() IN (SELECT user_id FROM admin_users));

-- Add these tables to realtime (only if not already added)
DO $$
BEGIN
    -- Add admin_users to realtime if not already there
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables
        WHERE pubname = 'supabase_realtime' AND tablename = 'admin_users'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE admin_users;
    END IF;

    -- Add counselor_profiles to realtime if not already there
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables
        WHERE pubname = 'supabase_realtime' AND tablename = 'counselor_profiles'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE counselor_profiles;
    END IF;

    -- Add counselor_couple_assignments to realtime if not already there
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables
        WHERE pubname = 'supabase_realtime' AND tablename = 'counselor_couple_assignments'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE counselor_couple_assignments;
    END IF;

    -- Add counseling_sessions to realtime if not already there
    IF NOT EXISTS (
        SELECT 1 FROM pg_publication_tables
        WHERE pubname = 'supabase_realtime' AND tablename = 'counseling_sessions'
    ) THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE counseling_sessions;
    END IF;
END $$;
