-- Create admin_users table first (for admin access control)
CREATE TABLE IF NOT EXISTS admin_users (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create assessment questions table
CREATE TABLE IF NOT EXISTS assessment_questions (
  id TEXT PRIMARY KEY,
  domain TEXT NOT NULL,
  type TEXT NOT NULL,
  text TEXT NOT NULL,
  options JSONB,
  required BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable row level security
ALTER TABLE assessment_questions ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
DROP POLICY IF EXISTS "Allow public read access" ON assessment_questions;
CREATE POLICY "Allow public read access"
  ON assessment_questions FOR SELECT
  USING (true);

-- Create policy for admin write access (you'll need to implement admin role)
DROP POLICY IF EXISTS "Allow admin write access" ON assessment_questions;
CREATE POLICY "Allow admin write access"
  ON assessment_questions FOR ALL
  USING (auth.uid() IN (SELECT user_id FROM admin_users));

-- Enable realtime
alter publication supabase_realtime add table assessment_questions;

-- Insert questions from the TypeScript file
-- Vision domain questions
INSERT INTO assessment_questions (id, domain, type, text, options, required)
VALUES
  ('vision_q1', 'vision', 'multiple-choice', 'How important is having a shared life vision with your partner?', '["Not important", "Somewhat important", "Neutral", "Important", "Very important"]', true),
  ('vision_q2', 'vision', 'scenario', 'If your partner received a job offer that required moving to another city, what would you expect to happen?', '["They should decline it to maintain our current lifestyle", "We would discuss it together and make a joint decision", "I would support their career and be willing to move", "We would try a long-distance relationship temporarily"]', true),
  ('vision_q3', 'vision', 'ranking', 'Rank the following life goals from most important (1) to least important (4):', '["Career advancement", "Family life", "Personal growth", "Community involvement"]', true),
  ('vision_q4', 'vision', 'open-ended', 'Describe where you see yourself and your relationship in 10 years:', null, false),
  
  -- Finances domain questions
  ('finances_q1', 'finances', 'multiple-choice', 'How important is financial transparency in your relationship?', '["Not important", "Somewhat important", "Neutral", "Important", "Very important"]', true),
  ('finances_q2', 'finances', 'scenario', 'If your partner received an unexpected bonus of $5,000, what would you expect them to do?', '["Save all of it for our future", "Discuss together how to allocate it", "Split it between savings and personal spending", "They should decide since they earned it"]', true),
  ('finances_q3', 'finances', 'ranking', 'Rank the following financial priorities from most important (1) to least important (4):', '["Building emergency savings", "Paying off debt", "Investing for retirement", "Saving for major purchases (home, car)"]', true),
  ('finances_q4', 'finances', 'open-ended', 'Describe your ideal approach to managing household finances with your partner:', null, false),
  
  -- Parenting domain questions
  ('parenting_q1', 'parenting', 'multiple-choice', 'How important is having a unified parenting approach with your partner?', '["Not important", "Somewhat important", "Neutral", "Important", "Very important"]', true),
  ('parenting_q2', 'parenting', 'scenario', 'If your child repeatedly breaks an important house rule, how would you handle it?', '["Strict consequences to teach discipline", "Discussion about why rules matter", "Natural consequences approach", "Flexible approach depending on circumstances"]', true),
  ('parenting_q3', 'parenting', 'ranking', 'Rank the following parenting values from most important (1) to least important (4):', '["Teaching responsibility", "Nurturing creativity", "Building strong character", "Academic achievement"]', true),
  ('parenting_q4', 'parenting', 'open-ended', 'Describe your philosophy on discipline and setting boundaries for children:', null, false),
  
  -- Communication domain questions
  ('communication_q1', 'communication', 'multiple-choice', 'How comfortable are you discussing difficult topics with your partner?', '["Very uncomfortable", "Somewhat uncomfortable", "Neutral", "Somewhat comfortable", "Very comfortable"]', true),
  ('communication_q2', 'communication', 'scenario', 'When you and your partner disagree on an important issue, what typically happens?', '["We avoid the topic to keep peace", "We discuss until we reach a compromise", "One of us usually gives in", "We seek outside help to resolve it"]', true),
  ('communication_q3', 'communication', 'ranking', 'Rank the following communication skills from most important (1) to least important (4):', '["Active listening", "Expressing feelings clearly", "Conflict resolution", "Non-verbal communication"]', true),
  ('communication_q4', 'communication', 'open-ended', 'Describe how you prefer to receive feedback or criticism from your partner:', null, false),
  
  -- Roles domain questions
  ('roles_q1', 'roles', 'multiple-choice', 'How important is having clearly defined roles in your relationship?', '["Not important", "Somewhat important", "Neutral", "Important", "Very important"]', true),
  ('roles_q2', 'roles', 'scenario', 'How should household responsibilities be divided?', '["Based on traditional gender roles", "Equally split all tasks", "Based on individual preferences and strengths", "Flexible and changing as needed"]', true),
  ('roles_q3', 'roles', 'ranking', 'Rank the following aspects of relationship roles from most important (1) to least important (4):', '["Equal partnership", "Clear responsibilities", "Flexibility to change", "Playing to individual strengths"]', true),
  ('roles_q4', 'roles', 'open-ended', 'Describe your ideal division of responsibilities in your household:', null, false),
  
  -- Sexuality domain questions
  ('sexuality_q1', 'sexuality', 'multiple-choice', 'How important is physical intimacy in maintaining a healthy relationship?', '["Not important", "Somewhat important", "Neutral", "Important", "Very important"]', true),
  ('sexuality_q2', 'sexuality', 'scenario', 'If you and your partner have different levels of desire for intimacy, how would you address it?', '["Compromise to meet in the middle", "Seek professional counseling", "Accept the difference and adjust expectations", "Open communication to find creative solutions"]', true),
  ('sexuality_q3', 'sexuality', 'ranking', 'Rank the following aspects of intimacy from most important (1) to least important (4):', '["Emotional connection", "Physical satisfaction", "Open communication", "Frequency of intimacy"]', true),
  ('sexuality_q4', 'sexuality', 'open-ended', 'Describe how you believe intimacy contributes to a healthy marriage:', null, false),
  
  -- Spirituality domain questions
  ('spirituality_q1', 'spirituality', 'multiple-choice', 'How important is sharing spiritual beliefs with your partner?', '["Not important", "Somewhat important", "Neutral", "Important", "Very important"]', true),
  ('spirituality_q2', 'spirituality', 'scenario', 'If you and your partner have different spiritual beliefs, how would you approach raising children?', '["Expose them to both beliefs and let them choose", "Agree on one primary spiritual tradition", "Focus on shared values rather than specific traditions", "Separate spiritual education based on each parent"]', true),
  ('spirituality_q3', 'spirituality', 'ranking', 'Rank the following spiritual practices from most important (1) to least important (4):', '["Regular worship attendance", "Prayer/meditation together", "Discussing spiritual topics", "Service to others/community"]', true),
  ('spirituality_q4', 'spirituality', 'open-ended', 'Describe how you envision spirituality playing a role in your marriage:', null, false),
  
  -- Darkside domain questions
  ('darkside_q1', 'darkside', 'multiple-choice', 'How comfortable are you discussing past traumas or mistakes with your partner?', '["Very uncomfortable", "Somewhat uncomfortable", "Neutral", "Somewhat comfortable", "Very comfortable"]', true),
  ('darkside_q2', 'darkside', 'scenario', 'If you notice unhealthy patterns from your family of origin appearing in your relationship, how would you address them?', '["Ignore them and focus on the present", "Discuss them openly with my partner", "Seek professional counseling", "Work on personal growth independently"]', true),
  ('darkside_q3', 'darkside', 'ranking', 'Rank the following potential relationship challenges from most concerning (1) to least concerning (4):', '["Communication breakdown", "Financial conflicts", "Different values/priorities", "External family interference"]', true),
  ('darkside_q4', 'darkside', 'open-ended', 'Describe any patterns or behaviors you''re concerned might negatively impact your relationship:', null, false)
ON CONFLICT (id) DO NOTHING;
